Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FEBA
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210285FF9, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB740  0002100690B4 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA20  00021006A49D (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB094F0000 ntdll.dll
7FFB089F0000 KERNEL32.DLL
7FFB06D70000 KERNELBASE.dll
7FFB09280000 USER32.dll
7FFB06A20000 win32u.dll
7FFB071F0000 GDI32.dll
7FFB06A50000 gdi32full.dll
7FFB06720000 msvcp_win.dll
7FFB06900000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB08810000 advapi32.dll
7FFB091D0000 msvcrt.dll
7FFB085B0000 sechost.dll
7FFB07150000 bcrypt.dll
7FFB08D20000 RPCRT4.dll
7FFB05C90000 CRYPTBASE.DLL
7FFB067C0000 bcryptPrimitives.dll
7FFB07B60000 IMM32.DLL
