# Employee Rating System | نظام تقييم الموظفين

![Employee Rating System](https://img.shields.io/badge/Employee%20Rating%20System-v2.0.0-blue)
![.NET](https://img.shields.io/badge/.NET-8.0-purple)
![Blazor](https://img.shields.io/badge/Blazor-Server-orange)
![License](https://img.shields.io/badge/License-MIT-green)

**A comprehensive bilingual employee performance evaluation system**  
**نظام شامل ثنائي اللغة لتقييم أداء الموظفين**

[English](#english) | [العربية](#arabic)



---

## English

### Project Overview

The Employee Rating System is a modern, comprehensive web application designed for organizations to manage employee performance evaluations efficiently. Built with C# Blazor Server technology, it provides a robust, scalable solution for performance management with full bilingual support (Arabic/English) and role-based access control.

**Key Highlights:**
- **Complete System**: 100% feature parity with enterprise requirements
- **Bilingual Interface**: Full Arabic/English support with RTL layout
- **Role-Based Security**: 4-tier access control system
- **Modern Architecture**: Built on .NET 8.0 Blazor Server
- **Production Ready**: Enterprise-grade security and scalability

### Features

#### Core Functionality
- **Comprehensive Evaluation System**: Multi-category performance assessments with weighted scoring
- **Hierarchical Department Management**: Multi-level organizational structure support
- **User Management**: Complete user lifecycle with approval workflows
- **Employee of the Month**: Automated selection based on performance metrics
- **Real-time Analytics**: Performance dashboards and reporting
- **File Attachments**: Document upload for evaluation evidence

#### Role-Based Access Control
- **SUPER_ADMIN**: Complete system administration and configuration
- **EXCELLENCE_TEAM**: Full quality assurance and evaluation oversight
- **SUPERVISOR**: Department-specific employee management and evaluation
- **EMPLOYEE**: Personal performance tracking and departmental visibility

#### Technical Features
- **Bilingual Support**: Seamless Arabic/English switching with RTL layout
- **Responsive Design**: Mobile-friendly interface with Bootstrap 5
- **Enterprise Security**: ASP.NET Core Identity with role-based authorization
- **Real-time Updates**: Blazor Server with SignalR integration
- **Multi-Database Support**: SQLite (dev), PostgreSQL, SQL Server (production)

### Technology Stack

#### Backend
- **Framework**: ASP.NET Core 8.0 Blazor Server
- **Database**: Entity Framework Core 8.0
- **Authentication**: ASP.NET Core Identity
- **Real-time**: SignalR for live updates

#### Frontend
- **UI Framework**: Bootstrap 5.3.0
- **Icons**: Font Awesome 6.0.0
- **Fonts**: Inter (Google Fonts) with Arabic support
- **Localization**: Built-in ASP.NET Core localization

#### Database Support
- **Development**: SQLite (lightweight, file-based)
- **Production**: PostgreSQL or SQL Server
- **Configuration**: Environment variable based switching

### Prerequisites

#### System Requirements
- **.NET 8.0 SDK** or higher
- **Visual Studio 2022** or **VS Code** with C# extension
- **Git** for version control

#### Database Options
- **SQLite** (included, no setup required)
- **PostgreSQL 12+** (recommended for production)
- **SQL Server 2019+** (enterprise environments)

###  Installation

#### 1. Clone Repository
```bash
git clone <repository-url>
cd rating/blazor_app
```

#### 2. Restore Dependencies
```bash
dotnet restore
```

#### 3. Database Setup
```bash

dotnet ef database update


dotnet ef database update
```

#### 4. Run Application
```bash
dotnet run
```

#### 5. Access Application
- **URL**: `http://localhost:5000`
- **Login**: Use demo credentials from [DEMO_CREDENTIALS.md](DEMO_CREDENTIALS.md)

### Configuration

#### Environment Variables
```json
{
  "DATABASE_ENGINE": "sqlite", // sqlite, postgresql, sqlserver
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=employee_rating.db",
    "PostgreSQL": "Host=localhost;Database=employee_rating;Username=user;Password=****",
    "SqlServer": "Server=localhost;Database=employee_rating;Trusted_Connection=true"
  }
}
```

#### Application Settings
```json
{
  "Application": {
    "Name": "Employee Rating System",
    "NameAr": "نظام تقييم الموظفين",
    "DefaultLanguage": "en",
    "SupportedLanguages": ["en", "ar"]
  }
}
```

### User Roles & Permissions

| Role | Access Level | Permissions |
|------|-------------|-------------|
| **SUPER_ADMIN** | Full System | Complete administration, all departments, all users |
| **EXCELLENCE_TEAM** | Quality Assurance | Full evaluation oversight, quality monitoring |
| **SUPERVISOR** | Department-Specific | Manage assigned department employees only |
| **EMPLOYEE** | Personal + Department | View own evaluations + departmental colleagues |

### 📖 Usage Guide

#### For Administrators
1. **User Management**: Create and approve user accounts
2. **Department Setup**: Configure organizational hierarchy
3. **Evaluation Categories**: Define assessment criteria and weights
4. **System Configuration**: Manage application settings

#### For Supervisors
1. **Employee Evaluation**: Conduct performance assessments
2. **Department Analytics**: View team performance metrics
3. **Approval Workflow**: Review and approve evaluations

#### For Employees
1. **Performance Tracking**: Monitor personal evaluation history
2. **Department Visibility**: View colleague performance (read-only)
3. **Language Preferences**: Switch between Arabic/English

### Project Structure

```
blazor_app/
├── Components/           # Blazor components and pages
│   ├── Layout/          # Application layout components
│   └── Pages/           # Application pages
├── Data/                # Database context and configurations
├── Models/              # Entity models and DTOs
├── Services/            # Business logic and data services
├── Resources/           # Localization resources
├── Migrations/          # Entity Framework migrations
├── wwwroot/            # Static files (CSS, JS, images)
└── Program.cs          # Application entry point
```



#### Common Issues

**Database Connection Issues**
```bash

dotnet ef database update --verbose
```

```



## Arabic

###  نظرة عامة على المشروع

نظام تقييم الموظفين هو تطبيق ويب حديث وشامل مصمم للمؤسسات لإدارة تقييمات أداء الموظفين بكفاءة. تم بناؤه باستخدام تقنية C# Blazor Server، ويوفر حلاً قوياً وقابلاً للتوسع لإدارة الأداء مع دعم كامل ثنائي اللغة (العربية/الإنجليزية) والتحكم في الوصول القائم على الأدوار.

**النقاط الرئيسية:**
-  **نظام متكامل**: توافق كامل 100% مع متطلبات المؤسسات
-  **واجهة ثنائية اللغة**: دعم كامل للعربية/الإنجليزية مع تخطيط RTL
- **أمان قائم على الأدوار**: نظام تحكم في الوصول من 4 مستويات
-  **هندسة معمارية حديثة**: مبني على .NET 8.0 Blazor Server
-  **جاهز للإنتاج**: أمان وقابلية توسع على مستوى المؤسسات

### الميزات

#### الوظائف الأساسية
- **نظام تقييم شامل**: تقييمات أداء متعددة الفئات مع نظام نقاط مرجح
- **إدارة الأقسام الهرمية**: دعم الهيكل التنظيمي متعدد المستويات
- **إدارة المستخدمين**: دورة حياة كاملة للمستخدم مع سير عمل الموافقة
- **موظف الشهر**: اختيار تلقائي بناءً على مقاييس الأداء
- **تحليلات فورية**: لوحات معلومات الأداء والتقارير
- **مرفقات الملفات**: تحميل المستندات لأدلة التقييم

#### التحكم في الوصول القائم على الأدوار
- **المدير العام**: إدارة النظام الكاملة والتكوين
- **فريق التميز**: ضمان الجودة الكامل والإشراف على التقييم
- **المشرف**: إدارة وتقييم موظفي القسم المحدد
- **الموظف**: تتبع الأداء الشخصي والرؤية القسمية

### المكدس التقني

#### الخلفية
- **الإطار**: ASP.NET Core 8.0 Blazor Server
- **قاعدة البيانات**: Entity Framework Core 8.0
- **المصادقة**: ASP.NET Core Identity
- **التحديثات الفورية**: SignalR للتحديثات المباشرة

#### الواجهة الأمامية
- **إطار واجهة المستخدم**: Bootstrap 5.3.0
- **الأيقونات**: Font Awesome 6.0.0
- **الخطوط**: Inter (Google Fonts) مع دعم العربية
- **الترجمة**: ترجمة ASP.NET Core المدمجة

###  المتطلبات المسبقة

#### متطلبات النظام
- **.NET 8.0 SDK** أو أحدث
- **Visual Studio 2022** أو **VS Code** مع امتداد C#
- **Git** للتحكم في الإصدار

###  التثبيت

#### 1. استنساخ المستودع
```bash
git clone <repository-url>
cd rating/blazor_app
```

#### 2. استعادة التبعيات
```bash
dotnet restore
```

#### 3. إعداد قاعدة البيانات
```bash
# لـ SQLite (افتراضي - لا يتطلب إعداد إضافي)
dotnet ef database update
```

#### 4. تشغيل التطبيق
```bash
dotnet run
```

#### 5. الوصول للتطبيق
- **الرابط**: `https://localhost:5001` أو `http://localhost:5000`
- **تسجيل الدخول**: استخدم بيانات الاعتماد التجريبية من [DEMO_CREDENTIALS.md](DEMO_CREDENTIALS.md)

###  أدوار المستخدمين والصلاحيات

| الدور | مستوى الوصول | الصلاحيات |
|------|-------------|-------------|
| **المدير العام** | النظام الكامل | إدارة كاملة، جميع الأقسام، جميع المستخدمين |
| **فريق التميز** | ضمان الجودة | إشراف كامل على التقييم، مراقبة الجودة |
| **المشرف** | خاص بالقسم | إدارة موظفي القسم المعين فقط |
| **الموظف** | شخصي + قسمي | عرض التقييمات الخاصة + زملاء القسم |

### دليل الاستخدام

#### للمديرين
1. **إدارة المستخدمين**: إنشاء وموافقة حسابات المستخدمين
2. **إعداد الأقسام**: تكوين الهيكل التنظيمي الهرمي
3. **فئات التقييم**: تحديد معايير التقييم والأوزان
4. **تكوين النظام**: إدارة إعدادات التطبيق

#### للمشرفين
1. **تقييم الموظفين**: إجراء تقييمات الأداء
2. **تحليلات القسم**: عرض مقاييس أداء الفريق
3. **سير عمل الموافقة**: مراجعة وموافقة التقييمات

#### للموظفين
1. **تتبع الأداء**: مراقبة تاريخ التقييم الشخصي
2. **رؤية القسم**: عرض أداء الزملاء (للقراءة فقط)
3. **تفضيلات اللغة**: التبديل بين العربية/الإنجليزية



