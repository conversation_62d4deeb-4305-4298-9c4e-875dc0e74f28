/* ===== EMPLOYEE RATING SYSTEM - PROFESSIONAL CORPORATE DESIGN ===== */

/* ===== BASE RESET AND VARIABLES ===== */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

:root {
    /* Professional Corporate Color Palette */
    --primary-color: #1e3a8a;        /* Deep Corporate Blue */
    --primary-light: #3b82f6;        /* Lighter Blue */
    --primary-dark: #1e40af;         /* Darker Blue */
    --secondary-color: #475569;      /* Professional Gray */
    --secondary-light: #64748b;      /* Light Gray */
    --secondary-dark: #334155;       /* Dark Gray */
    --success-color: #059669;        /* Corporate Green */
    --success-light: #10b981;        /* Light Green */
    --info-color: #0891b2;           /* Professional Teal */
    --warning-color: #d97706;        /* Professional Orange */
    --danger-color: #dc2626;         /* Professional Red */
    --light-color: #f8fafc;          /* Very Light Gray */
    --lighter-color: #f1f5f9;        /* Ultra Light Gray */
    --dark-color: #1e293b;           /* Dark Slate */
    --darker-color: #0f172a;         /* Very Dark Slate */
    --border-color: #e2e8f0;         /* Light Border */
    --border-light: #f1f5f9;         /* Very Light Border */
    --text-muted: #64748b;           /* Muted Text */
    --text-light: #94a3b8;           /* Light Text */

    /* Enhanced Design System */
    --border-radius: 8px;
    --border-radius-sm: 6px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --box-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --box-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --navbar-height: 60px;

    /* Typography Scale */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    /* Spacing Scale */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
}

/* ===== PROFESSIONAL TYPOGRAPHY ===== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700;800&display=swap');

:root {
    --font-family-english: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-family-arabic: 'Noto Sans Arabic', 'Amiri', 'Tahoma', 'Arial Unicode MS', sans-serif;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
}

/* Apply appropriate font based on language */
body {
    font-family: var(--font-family-english);
    font-weight: var(--font-weight-normal);
    letter-spacing: -0.01em;
}

[lang="ar"], [dir="rtl"] {
    font-family: var(--font-family-arabic);
    letter-spacing: 0;
}

/* Mixed content support */
.arabic-text {
    font-family: var(--font-family-arabic);
    letter-spacing: 0;
}

.english-text {
    font-family: var(--font-family-english);
    letter-spacing: -0.01em;
}

/* ===== ENHANCED GLOBAL LAYOUT ===== */
html {
    font-size: 16px;
    height: 100%;
    overflow-x: hidden;
    scroll-behavior: smooth;
    margin: 0;
    padding: 0;
}

body {
    background: linear-gradient(135deg, var(--lighter-color) 0%, var(--light-color) 100%);
    color: var(--dark-color);
    line-height: 1.6;
    min-height: 100vh;
    overflow-x: hidden;
    width: 100%;
    max-width: 100vw;
    font-size: var(--font-size-base);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    padding: 0;
}

/* ===== PROFESSIONAL LOGIN PAGE STYLES ===== */
.login-page {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--secondary-color) 100%);
    position: relative;
    overflow: hidden;
}

.login-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.login-page .container-fluid {
    min-height: 100vh;
    position: relative;
    z-index: 1;
}

.login-page .row {
    min-height: 100vh;
}

.login-form-container {
    padding: var(--spacing-2xl);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--box-shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-page .bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
}

.login-page .form-control-lg {
    padding: 1rem 1.25rem;
    font-size: var(--font-size-lg);
    border-radius: var(--border-radius-lg);
    border: 2px solid var(--border-color);
    transition: var(--transition);
    font-weight: var(--font-weight-medium);
    background-color: rgba(255, 255, 255, 0.9);
}

.login-page .form-control-lg:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(30, 58, 138, 0.15);
    background-color: #fff;
    transform: translateY(-1px);
}

.login-page .btn-lg {
    padding: 1rem 2rem;
    font-size: var(--font-size-lg);
    border-radius: var(--border-radius-lg);
    font-weight: var(--font-weight-semibold);
    transition: var(--transition);
    letter-spacing: 0.025em;
    text-transform: uppercase;
    position: relative;
    overflow: hidden;
}

.login-page .btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border: none;
    box-shadow: var(--box-shadow-md);
}

.login-page .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
}

.login-page .alert {
    border-radius: var(--border-radius-lg);
    border: none;
    font-weight: var(--font-weight-medium);
    box-shadow: var(--box-shadow);
}

.login-page .form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.125rem rgba(30, 58, 138, 0.25);
}

/* ===== PROFESSIONAL PROFILE AVATAR STYLES ===== */
.profile-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    box-shadow: var(--box-shadow);
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.profile-avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.profile-avatar:hover::before {
    transform: translateX(100%);
}

.profile-avatar:hover {
    transform: scale(1.05);
    box-shadow: var(--box-shadow-md);
}

.profile-avatar-sm {
    width: 32px;
    height: 32px;
    font-size: var(--font-size-xs);
}

.profile-avatar-lg {
    width: 48px;
    height: 48px;
    font-size: var(--font-size-lg);
}

.profile-avatar-xl {
    width: 64px;
    height: 64px;
    font-size: var(--font-size-xl);
}

/* ===== CONTAINER SYSTEM ===== */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding-left: 15px;
    padding-right: 15px;
}

.main-content {
    padding-top: calc(var(--navbar-height) + 1rem);
    padding-bottom: 1rem;
    min-height: calc(100vh - var(--navbar-height));
    width: 100%;
    overflow-x: hidden;
    margin: 0;
    position: relative;
    z-index: 1;
    clear: both;
}

.content-wrapper {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

/* Page wrapper fixes */
.page {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    position: relative;
}

/* Header section fixes */
.header-section {
    margin: 0;
    padding: 0;
    position: relative;
    z-index: 1030;
}

/* ===== BOOTSTRAP GRID SYSTEM ===== */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

/* ===== ENHANCED DASHBOARD STYLES ===== */
.stat-card {
    transition: var(--transition);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

/* Timeline Styles */
.timeline {
    position: relative;
}

.timeline-item {
    position: relative;
}

.timeline-marker {
    flex-shrink: 0;
}

.timeline-content {
    min-height: 40px;
    display: flex;
    align-items: center;
}

/* Gradient Background */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, #4f46e5 100%);
}

.stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    margin: 0 auto;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #20c997 100%) !important;
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%) !important;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #fd7e14 100%) !important;
}

/* Additional Bootstrap-style gradients */
.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-info {
    background-color: var(--info-color) !important;
}

.bg-success {
    background-color: var(--success-color) !important;
}

.bg-warning {
    background-color: var(--warning-color) !important;
}

.row > * {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

/* Column classes */
.col {
    flex: 1 0 0%;
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
    .col-sm { flex: 1 0 0%; }
    .col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
    .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-sm-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
    .col-sm-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-sm-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-sm-9 { flex: 0 0 75%; max-width: 75%; }
    .col-sm-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-sm-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
    .col-md { flex: 1 0 0%; }
    .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-md-3 { flex: 0 0 25%; max-width: 25%; }
    .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-md-6 { flex: 0 0 50%; max-width: 50%; }
    .col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-md-9 { flex: 0 0 75%; max-width: 75%; }
    .col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-md-12 { flex: 0 0 100%; max-width: 100%; }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
    .col-lg { flex: 1 0 0%; }
    .col-lg-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
    .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-lg-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
    .col-lg-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-lg-9 { flex: 0 0 75%; max-width: 75%; }
    .col-lg-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-lg-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-lg-12 { flex: 0 0 100%; max-width: 100%; }
}

/* ===== PROFESSIONAL CARD COMPONENTS ===== */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
    background-clip: border-box;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    margin-bottom: var(--spacing-xl);
    transition: var(--transition);
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 50%, var(--info-color) 100%);
    opacity: 0;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--box-shadow-xl);
    border-color: var(--primary-color);
}

.card:hover::before {
    opacity: 1;
}

.card-body {
    flex: 1 1 auto;
    padding: var(--spacing-xl);
    position: relative;
}

.card-title {
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--dark-color);
    line-height: 1.3;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.card-title i {
    color: var(--primary-color);
    font-size: var(--font-size-lg);
}

.card-text {
    margin-bottom: var(--spacing-lg);
    color: var(--text-muted);
    line-height: 1.6;
    font-weight: var(--font-weight-normal);
}

.card-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    margin-bottom: 0;
    background: linear-gradient(135deg, var(--lighter-color) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-bottom: 1px solid var(--border-color);
    font-weight: var(--font-weight-semibold);
    color: var(--dark-color);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: var(--spacing-xl);
    right: var(--spacing-xl);
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--primary-color) 50%, transparent 100%);
}

.card-footer {
    padding: var(--spacing-lg) var(--spacing-xl);
    background: linear-gradient(135deg, var(--lighter-color) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-top: 1px solid var(--border-color);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

/* Enhanced Card Variants */
.card-primary {
    border-color: var(--primary-color);
    box-shadow: 0 4px 6px -1px rgba(30, 58, 138, 0.1), 0 2px 4px -1px rgba(30, 58, 138, 0.06);
}

.card-primary .card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border-bottom-color: var(--primary-dark);
}

.card-success {
    border-color: var(--success-color);
    box-shadow: 0 4px 6px -1px rgba(5, 150, 105, 0.1), 0 2px 4px -1px rgba(5, 150, 105, 0.06);
}

.card-success .card-header {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);
    color: white;
    border-bottom-color: var(--success-color);
}

.card-info {
    border-color: var(--info-color);
    box-shadow: 0 4px 6px -1px rgba(8, 145, 178, 0.1), 0 2px 4px -1px rgba(8, 145, 178, 0.06);
}

.card-info .card-header {
    background: linear-gradient(135deg, var(--info-color) 0%, #0ea5e9 100%);
    color: white;
    border-bottom-color: var(--info-color);
}

/* ===== PROFESSIONAL BUTTON STYLES ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    font-weight: var(--font-weight-medium);
    line-height: 1.4;
    color: var(--dark-color);
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: 0.5rem 1rem;
    font-size: var(--font-size-sm);
    border-radius: var(--border-radius);
    transition: var(--transition);
    margin-right: 4px;
    margin-bottom: 4px;
    position: relative;
    overflow: hidden;
    letter-spacing: 0.01em;
    white-space: nowrap;
    min-height: 36px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.btn:active {
    transform: translateY(0);
    box-shadow: var(--box-shadow);
}

.btn-primary {
    color: #fff;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-color: var(--primary-color);
    box-shadow: var(--box-shadow);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
    border-color: var(--primary-light);
    color: #fff;
}

.btn-secondary {
    color: #fff;
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    border-color: var(--secondary-color);
    box-shadow: var(--box-shadow);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--secondary-light) 0%, var(--secondary-color) 100%);
    border-color: var(--secondary-light);
    color: #fff;
}

.btn-info {
    color: #fff;
    background: linear-gradient(135deg, var(--info-color) 0%, #0ea5e9 100%);
    border-color: var(--info-color);
    box-shadow: var(--box-shadow);
}

.btn-info:hover {
    background: linear-gradient(135deg, #0ea5e9 0%, var(--info-color) 100%);
    border-color: #0ea5e9;
    color: #fff;
}

.btn-success {
    color: #fff;
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);
    border-color: var(--success-color);
    box-shadow: var(--box-shadow);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--success-light) 0%, var(--success-color) 100%);
    border-color: var(--success-light);
    color: #fff;
}

.btn-warning {
    color: #fff;
    background: linear-gradient(135deg, var(--warning-color) 0%, #ea580c 100%);
    border-color: var(--warning-color);
    box-shadow: var(--box-shadow);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #ea580c 0%, var(--warning-color) 100%);
    border-color: #ea580c;
    color: #fff;
}

.btn-danger {
    color: #fff;
    background: linear-gradient(135deg, var(--danger-color) 0%, #ef4444 100%);
    border-color: var(--danger-color);
    box-shadow: var(--box-shadow);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ef4444 0%, var(--danger-color) 100%);
    border-color: #ef4444;
    color: #fff;
}

/* Button Sizes */
.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: var(--font-size-xs);
    border-radius: var(--border-radius-sm);
    min-height: 30px;
    gap: 2px;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: var(--font-size-base);
    border-radius: var(--border-radius-lg);
    font-weight: var(--font-weight-semibold);
    min-height: 44px;
    gap: 6px;
}

/* Outline Buttons */
.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
    background-color: transparent;
}

.btn-outline-primary:hover {
    color: #fff;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-color: var(--primary-color);
}

.btn-outline-secondary {
    color: var(--secondary-color);
    border-color: var(--secondary-color);
    background-color: transparent;
}

.btn-outline-secondary:hover {
    color: #fff;
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    border-color: var(--secondary-color);
}

/* ===== PROFESSIONAL FORM CONTROLS ===== */
.form-control {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: 1.5;
    color: var(--dark-color);
    background-color: #fff;
    background-image: none;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.form-control:focus {
    color: var(--dark-color);
    background-color: #fff;
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(30, 58, 138, 0.15), inset 0 1px 2px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.form-control::placeholder {
    color: var(--text-light);
    opacity: 1;
}

.form-select {
    display: block;
    width: 100%;
    padding: 0.75rem 2.25rem 0.75rem 1rem;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: 1.5;
    color: var(--dark-color);
    background-color: #fff;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    appearance: none;
}

.form-select:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(30, 58, 138, 0.15);
}

.form-label {
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-medium);
    color: var(--dark-color);
    font-size: var(--font-size-sm);
    letter-spacing: 0.025em;
}

.form-text {
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.form-check {
    display: block;
    min-height: 1.5rem;
    padding-left: 1.5em;
    margin-bottom: var(--spacing-sm);
}

.form-check-input {
    width: 1em;
    height: 1em;
    margin-top: 0.25em;
    margin-left: -1.5em;
    vertical-align: top;
    background-color: #fff;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    border: 2px solid var(--border-color);
    appearance: none;
    color-adjust: exact;
    transition: var(--transition);
}

.form-check-input[type="checkbox"] {
    border-radius: var(--border-radius-sm);
}

.form-check-input[type="radio"] {
    border-radius: 50%;
}

.form-check-input:active {
    filter: brightness(90%);
}

.form-check-input:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(30, 58, 138, 0.15);
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-check-input:checked[type="checkbox"] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}

.form-check-input:checked[type="radio"] {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
}

.form-check-label {
    color: var(--dark-color);
    font-weight: var(--font-weight-normal);
    cursor: pointer;
}

/* Input Groups */
.input-group {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: stretch;
    width: 100%;
}

.input-group > .form-control {
    position: relative;
    flex: 1 1 auto;
    width: 1%;
    min-width: 0;
}

.input-group-text {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: 1.5;
    color: var(--text-muted);
    text-align: center;
    white-space: nowrap;
    background-color: var(--lighter-color);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
}

/* ===== PROFESSIONAL TABLE STYLES ===== */
.table {
    width: 100%;
    margin-bottom: var(--spacing-lg);
    color: var(--dark-color);
    vertical-align: middle;
    border-color: var(--border-color);
    caption-side: bottom;
    border-collapse: collapse;
}

.table > :not(caption) > * > * {
    padding: 1rem 0.75rem;
    background-color: transparent;
    border-bottom: 1px solid var(--border-color);
    box-shadow: inset 0 0 0 9999px transparent;
}

.table > tbody {
    vertical-align: inherit;
}

.table > thead {
    vertical-align: bottom;
}

.table > :not(:first-child) {
    border-top: 2px solid var(--border-color);
}

.table-light {
    --bs-table-bg: var(--lighter-color);
    --bs-table-striped-bg: rgba(248, 250, 252, 0.5);
    --bs-table-striped-color: var(--dark-color);
    --bs-table-active-bg: rgba(30, 58, 138, 0.1);
    --bs-table-active-color: var(--dark-color);
    --bs-table-hover-bg: rgba(248, 250, 252, 0.8);
    --bs-table-hover-color: var(--dark-color);
    color: var(--dark-color);
    border-color: var(--border-color);
}

.table-hover > tbody > tr:hover > * {
    background-color: rgba(30, 58, 138, 0.05);
    transition: var(--transition);
}

.table-striped > tbody > tr:nth-of-type(odd) > * {
    background-color: rgba(248, 250, 252, 0.3);
}

.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    background: #fff;
}

.table-responsive .table {
    margin-bottom: 0;
}

/* Enhanced Table Headers */
.table thead th {
    font-weight: var(--font-weight-semibold);
    color: var(--dark-color);
    background: linear-gradient(135deg, var(--lighter-color) 0%, rgba(248, 250, 252, 0.8) 100%);
    border-bottom: 2px solid var(--primary-color);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: var(--font-size-sm);
    padding: 1.25rem 0.75rem;
    position: relative;
}

.table thead th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 50%, var(--info-color) 100%);
}

/* Professional Table Cells */
.table tbody td {
    font-weight: var(--font-weight-normal);
    vertical-align: middle;
    transition: var(--transition);
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Badge Styles for Tables */
.badge {
    display: inline-block;
    padding: 0.375em 0.75em;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--border-radius);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
}

.badge.bg-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #0ea5e9 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #ea580c 100%) !important;
    color: #fff !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #ef4444 100%) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%) !important;
}

/* Progress Bars */
.progress {
    display: flex;
    height: 0.75rem;
    overflow: hidden;
    font-size: var(--font-size-xs);
    background-color: var(--lighter-color);
    border-radius: var(--border-radius);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    transition: width 0.6s ease;
    position: relative;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(45deg, transparent 33%, rgba(255, 255, 255, 0.25) 50%, transparent 66%);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-bar.bg-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);
}

.progress-bar.bg-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #0ea5e9 100%);
}

.progress-bar.bg-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #ea580c 100%);
}

.progress-bar.bg-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #ef4444 100%);
}

/* ===== PROFESSIONAL DASHBOARD STYLES ===== */
.stat-card {
    transition: var(--transition);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    position: relative;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    box-shadow: var(--box-shadow);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 50%, var(--info-color) 100%);
    opacity: 0;
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--box-shadow-xl);
    border-color: var(--primary-color);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card .card-body {
    padding: var(--spacing-2xl);
    text-align: center;
    position: relative;
}

.stat-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    margin: 0 auto var(--spacing-lg);
    box-shadow: var(--box-shadow-lg);
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.8s;
}

.stat-card:hover .stat-icon::before {
    transform: translateX(100%);
}

.stat-icon i {
    font-size: 2rem;
    color: #fff;
    z-index: 1;
    position: relative;
}

.stat-value {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
    line-height: 1.2;
}

.stat-label {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--spacing-lg);
}

.stat-change {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
}

.stat-change.positive {
    background-color: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
}

.stat-change.negative {
    background-color: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

.stat-change.neutral {
    background-color: rgba(100, 116, 139, 0.1);
    color: var(--text-muted);
}

/* Dashboard Grid Layout */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

/* Chart Container */
.chart-container {
    position: relative;
    background: #fff;
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--box-shadow);
    border: 1px solid var(--border-color);
    margin-bottom: var(--spacing-xl);
}

.chart-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--dark-color);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

/* Timeline Styles */
.timeline {
    position: relative;
    padding-left: var(--spacing-xl);
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.timeline-item {
    position: relative;
    margin-bottom: var(--spacing-xl);
    padding-left: var(--spacing-xl);
}

.timeline-marker {
    position: absolute;
    left: -0.5rem;
    top: 0.5rem;
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border: 3px solid #fff;
    box-shadow: var(--box-shadow);
    z-index: 1;
}

.timeline-content {
    background: #fff;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.timeline-content:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.timeline-date {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-xs);
}

.timeline-title {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
}

.timeline-description {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    line-height: 1.6;
}

/* ===== PROFESSIONAL TYPOGRAPHY & SPACING ===== */
h1, .h1 {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    line-height: 1.2;
    color: var(--dark-color);
    margin-bottom: var(--spacing-lg);
    letter-spacing: -0.025em;
}

h2, .h2 {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-semibold);
    line-height: 1.3;
    color: var(--dark-color);
    margin-bottom: var(--spacing-lg);
    letter-spacing: -0.02em;
}

h3, .h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    line-height: 1.4;
    color: var(--dark-color);
    margin-bottom: var(--spacing-md);
    letter-spacing: -0.015em;
}

h4, .h4 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-medium);
    line-height: 1.4;
    color: var(--dark-color);
    margin-bottom: var(--spacing-md);
    letter-spacing: -0.01em;
}

h5, .h5 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    line-height: 1.5;
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
}

h6, .h6 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    line-height: 1.5;
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

p {
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
    color: var(--dark-color);
}

.lead {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-normal);
    line-height: 1.6;
    color: var(--text-muted);
}

.text-small {
    font-size: var(--font-size-sm);
}

.text-large {
    font-size: var(--font-size-lg);
}

/* Professional Text Colors */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-dark { color: var(--dark-color) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-light { color: var(--text-light) !important; }

/* Font Weights */
.fw-light { font-weight: var(--font-weight-light) !important; }
.fw-normal { font-weight: var(--font-weight-normal) !important; }
.fw-medium { font-weight: var(--font-weight-medium) !important; }
.fw-semibold { font-weight: var(--font-weight-semibold) !important; }
.fw-bold { font-weight: var(--font-weight-bold) !important; }
.fw-extrabold { font-weight: var(--font-weight-extrabold) !important; }

/* Professional Spacing System */
.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }
.p-6 { padding: var(--spacing-2xl) !important; }
.p-7 { padding: var(--spacing-3xl) !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: var(--spacing-xs) !important; }
.pt-2 { padding-top: var(--spacing-sm) !important; }
.pt-3 { padding-top: var(--spacing-md) !important; }
.pt-4 { padding-top: var(--spacing-lg) !important; }
.pt-5 { padding-top: var(--spacing-xl) !important; }
.pt-6 { padding-top: var(--spacing-2xl) !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: var(--spacing-xs) !important; }
.pb-2 { padding-bottom: var(--spacing-sm) !important; }
.pb-3 { padding-bottom: var(--spacing-md) !important; }
.pb-4 { padding-bottom: var(--spacing-lg) !important; }
.pb-5 { padding-bottom: var(--spacing-xl) !important; }
.pb-6 { padding-bottom: var(--spacing-2xl) !important; }

.ps-0 { padding-left: 0 !important; }
.ps-1 { padding-left: var(--spacing-xs) !important; }
.ps-2 { padding-left: var(--spacing-sm) !important; }
.ps-3 { padding-left: var(--spacing-md) !important; }
.ps-4 { padding-left: var(--spacing-lg) !important; }
.ps-5 { padding-left: var(--spacing-xl) !important; }

.pe-0 { padding-right: 0 !important; }
.pe-1 { padding-right: var(--spacing-xs) !important; }
.pe-2 { padding-right: var(--spacing-sm) !important; }
.pe-3 { padding-right: var(--spacing-md) !important; }
.pe-4 { padding-right: var(--spacing-lg) !important; }
.pe-5 { padding-right: var(--spacing-xl) !important; }

.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.m-5 { margin: var(--spacing-xl) !important; }
.m-6 { margin: var(--spacing-2xl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mt-5 { margin-top: var(--spacing-xl) !important; }
.mt-6 { margin-top: var(--spacing-2xl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }
.mb-6 { margin-bottom: var(--spacing-2xl) !important; }

.ms-0 { margin-left: 0 !important; }
.ms-1 { margin-left: var(--spacing-xs) !important; }
.ms-2 { margin-left: var(--spacing-sm) !important; }
.ms-3 { margin-left: var(--spacing-md) !important; }
.ms-4 { margin-left: var(--spacing-lg) !important; }
.ms-5 { margin-left: var(--spacing-xl) !important; }

.me-0 { margin-right: 0 !important; }
.me-1 { margin-right: var(--spacing-xs) !important; }
.me-2 { margin-right: var(--spacing-sm) !important; }
.me-3 { margin-right: var(--spacing-md) !important; }
.me-4 { margin-right: var(--spacing-lg) !important; }
.me-5 { margin-right: var(--spacing-xl) !important; }

.gap-0 { gap: 0 !important; }
.gap-1 { gap: var(--spacing-xs) !important; }
.gap-2 { gap: var(--spacing-sm) !important; }
.gap-3 { gap: var(--spacing-md) !important; }
.gap-4 { gap: var(--spacing-lg) !important; }
.gap-5 { gap: var(--spacing-xl) !important; }

/* ===== PROFESSIONAL ANIMATIONS & TRANSITIONS ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in-left {
    animation: slideInFromLeft 0.6s ease-out;
}

.slide-in-right {
    animation: slideInFromRight 0.6s ease-out;
}

.scale-in {
    animation: scaleIn 0.4s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

.float-animation {
    animation: float 3s ease-in-out infinite;
}

/* Hover Effects */
.hover-lift {
    transition: var(--transition);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--box-shadow-lg);
}

.hover-scale {
    transition: var(--transition);
}

.hover-scale:hover {
    transform: scale(1.02);
}

.hover-glow {
    transition: var(--transition);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(30, 58, 138, 0.3);
}

.hover-rotate {
    transition: var(--transition);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* Loading States */
.loading-shimmer {
    background: linear-gradient(90deg, var(--lighter-color) 25%, rgba(255, 255, 255, 0.8) 50%, var(--lighter-color) 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

.loading-pulse {
    animation: pulse 1.5s ease-in-out infinite;
    background-color: var(--lighter-color);
}

/* Staggered Animations */
.stagger-animation > * {
    opacity: 0;
    animation: fadeIn 0.6s ease-out forwards;
}

.stagger-animation > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-animation > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-animation > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-animation > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-animation > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-animation > *:nth-child(6) { animation-delay: 0.6s; }

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus States */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 0.25rem rgba(30, 58, 138, 0.25);
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Professional Micro-interactions */
.interactive-element {
    transition: var(--transition);
    cursor: pointer;
}

.interactive-element:hover {
    transform: translateY(-1px);
}

.interactive-element:active {
    transform: translateY(0);
}

/* Page Transitions */
.page-enter {
    opacity: 0;
    transform: translateY(20px);
}

.page-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.page-exit {
    opacity: 1;
    transform: translateY(0);
}

.page-exit-active {
    opacity: 0;
    transform: translateY(-20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* ===== PROFESSIONAL RESPONSIVE DESIGN ===== */
/* Container System */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
}

.container-fluid {
    width: 100%;
    padding-left: var(--spacing-lg);
    padding-right: var(--spacing-lg);
}

/* Duplicate .main-content definition removed - using the one defined earlier */

.content-wrapper {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
}

/* Grid System */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: calc(var(--spacing-lg) * -0.5);
    margin-left: calc(var(--spacing-lg) * -0.5);
}

.row > * {
    position: relative;
    width: 100%;
    padding-right: calc(var(--spacing-lg) * 0.5);
    padding-left: calc(var(--spacing-lg) * 0.5);
}

/* Column classes */
.col { flex: 1 0 0%; }
.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* Tablet Responsive (768px and up) */
@media (min-width: 768px) {
    .container {
        max-width: 750px;
    }

    .col-md { flex: 1 0 0%; }
    .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-md-3 { flex: 0 0 25%; max-width: 25%; }
    .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-md-6 { flex: 0 0 50%; max-width: 50%; }
    .col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-md-9 { flex: 0 0 75%; max-width: 75%; }
    .col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-md-12 { flex: 0 0 100%; max-width: 100%; }

    .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }

    .stat-card .card-body {
        padding: var(--spacing-xl);
    }

    .navbar-expand-lg .navbar-nav {
        flex-direction: row;
    }

    .navbar-expand-lg .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
        margin: 0 var(--spacing-xs);
    }
}

/* Desktop Responsive (992px and up) */
@media (min-width: 992px) {
    .container {
        max-width: 970px;
    }

    .col-lg { flex: 1 0 0%; }
    .col-lg-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
    .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-lg-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
    .col-lg-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-lg-9 { flex: 0 0 75%; max-width: 75%; }
    .col-lg-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-lg-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-lg-12 { flex: 0 0 100%; max-width: 100%; }

    .dashboard-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }

    .col-xl { flex: 1 0 0%; }
    .col-xl-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-xl-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-xl-3 { flex: 0 0 25%; max-width: 25%; }
    .col-xl-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-xl-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .col-xl-6 { flex: 0 0 50%; max-width: 50%; }
    .col-xl-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .col-xl-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .col-xl-9 { flex: 0 0 75%; max-width: 75%; }
    .col-xl-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .col-xl-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .col-xl-12 { flex: 0 0 100%; max-width: 100%; }
}

/* Mobile Responsive (up to 767px) */
@media (max-width: 767.98px) {
    .container, .container-fluid {
        padding-left: var(--spacing-md);
        padding-right: var(--spacing-md);
    }

    .row {
        margin-right: calc(var(--spacing-md) * -0.5);
        margin-left: calc(var(--spacing-md) * -0.5);
    }

    .row > * {
        padding-right: calc(var(--spacing-md) * 0.5);
        padding-left: calc(var(--spacing-md) * 0.5);
    }

    .main-content {
        padding-top: calc(var(--navbar-height) + 0.75rem);
        padding-bottom: 0.75rem;
    }

    .card-body {
        padding: var(--spacing-lg);
    }

    .stat-card .card-body {
        padding: var(--spacing-lg);
        text-align: center;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        margin-bottom: var(--spacing-md);
    }

    .stat-icon i {
        font-size: 1.5rem;
    }

    .stat-value {
        font-size: var(--font-size-2xl);
    }

    .btn {
        width: 100%;
        margin-bottom: var(--spacing-sm);
        margin-right: 0;
    }

    .btn-group .btn {
        width: auto;
    }

    .table-responsive {
        font-size: var(--font-size-sm);
    }

    .table thead th {
        padding: 0.75rem 0.5rem;
        font-size: var(--font-size-xs);
    }

    .table tbody td {
        padding: 0.75rem 0.5rem;
    }

    .navbar-brand {
        font-size: var(--font-size-base);
        max-width: 200px;
    }

    .navbar-brand span {
        font-size: var(--font-size-sm);
    }

    .navbar-nav {
        padding-top: var(--spacing-md);
        width: 100%;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--border-color);
        width: 100%;
        font-size: var(--font-size-sm);
    }

    .dropdown-menu {
        position: static !important;
        float: none;
        width: 100%;
        margin-top: 0;
        background-color: var(--lighter-color);
        border: none;
        box-shadow: none;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .timeline {
        padding-left: var(--spacing-lg);
    }

    .timeline-item {
        padding-left: var(--spacing-lg);
    }

    h1, .h1 { font-size: var(--font-size-3xl); }
    h2, .h2 { font-size: var(--font-size-2xl); }
    h3, .h3 { font-size: var(--font-size-xl); }
    h4, .h4 { font-size: var(--font-size-lg); }

    .form-control, .form-select {
        font-size: var(--font-size-base);
    }

    .modal-dialog {
        margin: var(--spacing-md);
    }

    .card-title {
        font-size: var(--font-size-lg);
    }
}

/* Small Mobile (up to 575px) */
@media (max-width: 575.98px) {
    .container, .container-fluid {
        padding-left: var(--spacing-sm);
        padding-right: var(--spacing-sm);
    }

    .row {
        margin-right: calc(var(--spacing-sm) * -0.5);
        margin-left: calc(var(--spacing-sm) * -0.5);
    }

    .row > * {
        padding-right: calc(var(--spacing-sm) * 0.5);
        padding-left: calc(var(--spacing-sm) * 0.5);
    }

    .card-body {
        padding: var(--spacing-md);
    }

    .stat-card .card-body {
        padding: var(--spacing-md);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
    }

    .stat-icon i {
        font-size: 1.25rem;
    }

    .stat-value {
        font-size: var(--font-size-xl);
    }

    .navbar-brand {
        font-size: var(--font-size-sm);
        max-width: 150px;
    }

    .navbar-brand span {
        font-size: var(--font-size-xs);
        line-height: 1.1;
    }

    .table thead th,
    .table tbody td {
        padding: 0.5rem 0.25rem;
        font-size: var(--font-size-xs);
    }

    h1, .h1 { font-size: var(--font-size-2xl); }
    h2, .h2 { font-size: var(--font-size-xl); }
    h3, .h3 { font-size: var(--font-size-lg); }

    .btn {
        padding: 0.625rem 1rem;
        font-size: var(--font-size-sm);
    }

    .btn-lg {
        padding: 0.75rem 1.25rem;
        font-size: var(--font-size-base);
    }

    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: var(--font-size-xs);
    }
}

/* Utility Classes for Responsive Design */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

@media (max-width: 575.98px) {
    .d-sm-none { display: none !important; }
    .d-sm-inline { display: inline !important; }
    .d-sm-inline-block { display: inline-block !important; }
    .d-sm-block { display: block !important; }
    .d-sm-flex { display: flex !important; }
    .d-sm-inline-flex { display: inline-flex !important; }
}

@media (max-width: 767.98px) {
    .d-md-none { display: none !important; }
    .d-md-inline { display: inline !important; }
    .d-md-inline-block { display: inline-block !important; }
    .d-md-block { display: block !important; }
    .d-md-flex { display: flex !important; }
    .d-md-inline-flex { display: inline-flex !important; }
}

@media (max-width: 991.98px) {
    .d-lg-none { display: none !important; }
    .d-lg-inline { display: inline !important; }
    .d-lg-inline-block { display: inline-block !important; }
    .d-lg-block { display: block !important; }
    .d-lg-flex { display: flex !important; }
    .d-lg-inline-flex { display: inline-flex !important; }
}

/* ===== PROFESSIONAL UTILITY CLASSES ===== */
/* Flexbox Utilities */
.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.flex-fill { flex: 1 1 auto !important; }
.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

/* Text Utilities */
.text-start { text-align: left !important; }
.text-end { text-align: right !important; }
.text-center { text-align: center !important; }
.text-justify { text-align: justify !important; }

.text-wrap { white-space: normal !important; }
.text-nowrap { white-space: nowrap !important; }
.text-break { word-wrap: break-word !important; word-break: break-word !important; }

.text-lowercase { text-transform: lowercase !important; }
.text-uppercase { text-transform: uppercase !important; }
.text-capitalize { text-transform: capitalize !important; }

.text-decoration-none { text-decoration: none !important; }
.text-decoration-underline { text-decoration: underline !important; }
.text-decoration-line-through { text-decoration: line-through !important; }

/* Position Utilities */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* Border Utilities */
.border { border: 1px solid var(--border-color) !important; }
.border-0 { border: 0 !important; }
.border-top { border-top: 1px solid var(--border-color) !important; }
.border-end { border-right: 1px solid var(--border-color) !important; }
.border-bottom { border-bottom: 1px solid var(--border-color) !important; }
.border-start { border-left: 1px solid var(--border-color) !important; }

.border-primary { border-color: var(--primary-color) !important; }
.border-secondary { border-color: var(--secondary-color) !important; }
.border-success { border-color: var(--success-color) !important; }
.border-info { border-color: var(--info-color) !important; }
.border-warning { border-color: var(--warning-color) !important; }
.border-danger { border-color: var(--danger-color) !important; }

.rounded { border-radius: var(--border-radius) !important; }
.rounded-0 { border-radius: 0 !important; }
.rounded-1 { border-radius: var(--border-radius-sm) !important; }
.rounded-2 { border-radius: var(--border-radius) !important; }
.rounded-3 { border-radius: var(--border-radius-lg) !important; }
.rounded-4 { border-radius: var(--border-radius-xl) !important; }
.rounded-circle { border-radius: 50% !important; }
.rounded-pill { border-radius: 50rem !important; }

/* Shadow Utilities */
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: var(--box-shadow) !important; }
.shadow { box-shadow: var(--box-shadow-md) !important; }
.shadow-lg { box-shadow: var(--box-shadow-lg) !important; }
.shadow-xl { box-shadow: var(--box-shadow-xl) !important; }

/* Background Utilities */
.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-light { background-color: var(--light-color) !important; }
.bg-dark { background-color: var(--dark-color) !important; }
.bg-white { background-color: #fff !important; }
.bg-transparent { background-color: transparent !important; }

/* Width and Height Utilities */
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

.mw-100 { max-width: 100% !important; }
.mh-100 { max-height: 100% !important; }

.min-vw-100 { min-width: 100vw !important; }
.min-vh-100 { min-height: 100vh !important; }

.vw-100 { width: 100vw !important; }
.vh-100 { height: 100vh !important; }

/* Overflow Utilities */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

/* Professional Enhancement Classes */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
}

.gradient-secondary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
}

.gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);
}

.text-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.corporate-shadow {
    box-shadow: 0 10px 25px -5px rgba(30, 58, 138, 0.1), 0 10px 10px -5px rgba(30, 58, 138, 0.04);
}

.professional-border {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
}

.professional-border:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(30, 58, 138, 0.15);
}

/* Print Styles */
@media print {
    .no-print { display: none !important; }

    body {
        background: white !important;
        color: black !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }

    .btn {
        border: 1px solid #000 !important;
        background: white !important;
        color: black !important;
    }

    .navbar,
    .sidebar,
    .footer {
        display: none !important;
    }

    .main-content {
        padding-top: 0 !important;
        margin: 0 !important;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000;
        --text-muted: #333;
    }

    .card {
        border: 2px solid #000;
    }

    .btn {
        border: 2px solid #000;
    }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #3b82f6;
        --secondary-color: #64748b;
        --success-color: #10b981;
        --info-color: #06b6d4;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --light-color: #1e293b;
        --lighter-color: #334155;
        --dark-color: #f8fafc;
        --darker-color: #e2e8f0;
        --border-color: #475569;
        --text-muted: #94a3b8;
        --text-light: #64748b;
    }

    body {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        color: #f8fafc;
    }

    .card {
        background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(51, 65, 85, 0.9) 100%);
    }

    .navbar {
        background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.95) 100%);
    }
}

/* ===== LAYOUT AND SPACING FIXES ===== */
/* Fix any potential margin/padding conflicts */
* {
    box-sizing: border-box;
}

/* Ensure no unwanted margins on key layout elements */
.container, .container-fluid {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

/* Fix for any Bootstrap conflicts */
.navbar-fixed-top + .main-content,
.navbar.fixed-top + .main-content {
    margin-top: 0 !important;
}

/* Ensure proper spacing for fixed navbar */
body.has-navbar-fixed-top {
    padding-top: 0 !important;
    margin-top: 0 !important;
}

/* ===== NAVIGATION SPACING FIXES ===== */
/* Fix for language switcher and user menu spacing */
.navbar-nav .nav-item.d-flex {
    margin-left: 8px;
    margin-right: 8px;
}

.navbar-nav .nav-item .dropdown-toggle {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.navbar-nav .nav-item .dropdown-toggle .d-none.d-md-inline {
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Profile avatar sizing fixes */
.profile-avatar-sm {
    width: 28px;
    height: 28px;
    font-size: 0.7rem;
    flex-shrink: 0;
}

/* Language switcher button fixes */
.language-switcher .btn {
    padding: 0.25rem 0.5rem;
    font-size: var(--font-size-xs);
    min-height: 28px;
    margin: 0 2px;
}

/* Navbar toggler fixes */
.navbar-toggler {
    padding: 0.25rem 0.5rem;
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
}

/* Container spacing adjustments */
.navbar .container {
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
}

@media (max-width: 991.98px) {
    .navbar .container {
        padding-left: var(--spacing-xs);
        padding-right: var(--spacing-xs);
    }

    .navbar-brand {
        margin-right: var(--spacing-sm);
    }

    .navbar-nav .nav-item.d-flex {
        margin: var(--spacing-xs) 0;
    }
}

@media (max-width: 767.98px) {
    .navbar-brand span {
        display: none;
    }

    .navbar-brand i {
        font-size: var(--font-size-lg);
    }

    .navbar-nav .nav-item .dropdown-toggle .d-none.d-md-inline {
        display: none !important;
    }
}

/* ===== GRADIENT BACKGROUNDS ===== */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
}

.bg-gradient-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #20c997 100%);
    color: white;
}

.bg-gradient-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
    color: white;
}

.bg-gradient-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #fd7e14 100%);
    color: white;
}

/* ===== UTILITY CLASSES ===== */
.d-flex { display: flex !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-none { display: none !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-column { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-center { align-items: center !important; }
.align-items-end { align-items: flex-end !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-between { justify-content: space-between !important; }

.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.text-end { text-align: right !important; }

.text-white { color: #fff !important; }
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: #6c757d !important; }
.text-success { color: var(--success-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }

.bg-white { background-color: #fff !important; }
.bg-light { background-color: var(--light-color) !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

.m-0 { margin: 0 !important; }
.m-1 { margin: 0.25rem !important; }
.m-2 { margin: 0.5rem !important; }
.m-3 { margin: 1rem !important; }
.m-4 { margin: 1.5rem !important; }
.m-5 { margin: 3rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.me-0 { margin-right: 0 !important; }
.me-1 { margin-right: 0.25rem !important; }
.me-2 { margin-right: 0.5rem !important; }
.me-3 { margin-right: 1rem !important; }
.me-4 { margin-right: 1.5rem !important; }
.me-5 { margin-right: 3rem !important; }

.ms-0 { margin-left: 0 !important; }
.ms-1 { margin-left: 0.25rem !important; }
.ms-2 { margin-left: 0.5rem !important; }
.ms-3 { margin-left: 1rem !important; }
.ms-4 { margin-left: 1.5rem !important; }
.ms-5 { margin-left: 3rem !important; }

.gap-0 { gap: 0 !important; }
.gap-1 { gap: 0.25rem !important; }
.gap-2 { gap: 0.5rem !important; }
.gap-3 { gap: 1rem !important; }
.gap-4 { gap: 1.5rem !important; }
.gap-5 { gap: 3rem !important; }

.rounded { border-radius: var(--border-radius) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }

.shadow { box-shadow: var(--box-shadow) !important; }
.shadow-lg { box-shadow: var(--box-shadow-lg) !important; }

.fw-normal { font-weight: 400 !important; }
.fw-bold { font-weight: 700 !important; }
.fw-bolder { font-weight: 900 !important; }

/* ===== RESPONSIVE UTILITIES ===== */
@media (max-width: 575.98px) {
    .d-sm-none { display: none !important; }
    .d-sm-block { display: block !important; }
    .text-sm-center { text-align: center !important; }
}

@media (max-width: 767.98px) {
    .d-md-none { display: none !important; }
    .d-md-block { display: block !important; }
    .text-md-center { text-align: center !important; }
}

@media (max-width: 991.98px) {
    .d-lg-none { display: none !important; }
    .d-lg-block { display: block !important; }
    .text-lg-center { text-align: center !important; }
}

/* ===== PROFESSIONAL NAVIGATION STYLES ===== */
.navbar {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    min-height: 60px;
}

.navbar.fixed-top {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
}

.navbar-brand {
    padding: 0.25rem 0;
    margin-right: var(--spacing-md);
    font-size: var(--font-size-lg);
    text-decoration: none;
    white-space: nowrap;
    color: var(--dark-color);
    font-weight: var(--font-weight-bold);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    max-width: 280px;
    overflow: hidden;
}

.navbar-brand:hover {
    color: var(--primary-color);
    transform: translateY(-1px);
}

.navbar-brand i {
    font-size: var(--font-size-xl);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    flex-shrink: 0;
}

.navbar-brand span {
    font-size: var(--font-size-base);
    line-height: 1.2;
    overflow: hidden;
    text-overflow: ellipsis;
}

.navbar-nav {
    display: flex;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
    gap: 2px;
}

.navbar-nav .nav-link {
    padding: 0.5rem 0.75rem;
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
    font-weight: var(--font-weight-medium);
    border-radius: var(--border-radius);
    position: relative;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: var(--font-size-sm);
    white-space: nowrap;
    min-height: 36px;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color);
    background-color: rgba(30, 58, 138, 0.05);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    color: var(--primary-color);
    background-color: rgba(30, 58, 138, 0.1);
    font-weight: var(--font-weight-semibold);
}

.navbar-expand-lg .navbar-nav {
    flex-direction: row;
    gap: 4px;
}

.navbar-expand-lg .navbar-nav .nav-link {
    padding: 0.5rem 0.75rem;
    margin: 0 2px;
}

/* Dropdown Enhancements */
.dropdown-menu {
    border: none;
    box-shadow: var(--box-shadow-lg);
    border-radius: var(--border-radius);
    padding: 0.25rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    margin-top: 2px;
    min-width: 180px;
    max-width: 250px;
}

.dropdown-item {
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius-sm);
    transition: var(--transition);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: var(--font-size-sm);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 32px;
}

.dropdown-item:hover {
    background-color: var(--lighter-color);
    color: var(--primary-color);
    transform: translateX(2px);
}

.dropdown-divider {
    margin: 0.25rem 0;
    border-color: var(--border-color);
}

.dropdown-header {
    font-weight: var(--font-weight-semibold);
    color: var(--dark-color);
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.25rem 0.75rem;
    line-height: 1.2;
}

/* ===== FOOTER STYLES ===== */
.footer {
    background-color: var(--dark-color);
    color: #fff;
    padding: 2rem 0;
    margin-top: auto;
}

.footer p {
    margin-bottom: 0;
    font-size: 0.875rem;
}

/* ===== ENHANCED RESPONSIVE LAYOUT ===== */
@media (max-width: 768px) {
    .container, .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }

    .row {
        margin-right: -15px;
        margin-left: -15px;
    }

    .row > * {
        padding-right: 15px;
        padding-left: 15px;
    }

    .main-content {
        padding-top: calc(var(--navbar-height) + 1rem);
        padding-bottom: 1rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .stat-card .card-body {
        padding: 2rem 1rem;
    }

    .btn {
        margin-bottom: 0.5rem;
    }

    /* Improve mobile navigation */
    .navbar-nav {
        padding-top: 1rem;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(0,0,0,0.1);
    }
}

@media (max-width: 576px) {
    .container, .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }

    .row {
        margin-right: -10px;
        margin-left: -10px;
    }

    .row > * {
        padding-right: 10px;
        padding-left: 10px;
    }

    .card-body {
        padding: 1rem;
    }

    .stat-card .card-body {
        padding: 1.5rem 0.75rem;
    }

    .card-title {
        font-size: 1.25rem;
    }

    /* Improve small screen layout */
    .stat-card h3 {
        font-size: 1.75rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
    }

    .stat-icon i {
        font-size: 1.25rem;
    }
}

/* ===== ADDITIONAL LAYOUT IMPROVEMENTS ===== */
.card {
    border-radius: var(--border-radius-lg);
    border: 1px solid rgba(0,0,0,0.08);
}

.card-header {
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    background: rgba(0,0,0,0.02);
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

/* Fix any potential z-index issues */
.navbar {
    z-index: 1030;
}

.dropdown-menu {
    z-index: 1040;
}

/* ===== RTL SUPPORT AND BILINGUAL LAYOUT ===== */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] .navbar-brand {
    margin-right: 0;
    margin-left: auto;
}

[dir="rtl"] .navbar-nav {
    margin-right: auto;
    margin-left: 0;
}

[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
}

[dir="rtl"] .text-end,
.rtl-layout .text-end {
    text-align: left !important;
}

[dir="rtl"] .text-start,
.rtl-layout .text-start {
    text-align: right !important;
}

[dir="rtl"] .me-auto {
    margin-right: 0 !important;
    margin-left: auto !important;
}

[dir="rtl"] .ms-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
}

/* RTL spacing utilities */
[dir="rtl"] .me-1 { margin-right: 0 !important; margin-left: 0.25rem !important; }
[dir="rtl"] .me-2 { margin-right: 0 !important; margin-left: 0.5rem !important; }
[dir="rtl"] .me-3 { margin-right: 0 !important; margin-left: 1rem !important; }
[dir="rtl"] .ms-1 { margin-left: 0 !important; margin-right: 0.25rem !important; }
[dir="rtl"] .ms-2 { margin-left: 0 !important; margin-right: 0.5rem !important; }
[dir="rtl"] .ms-3 { margin-left: 0 !important; margin-right: 1rem !important; }

/* RTL card layout */
[dir="rtl"] .card-header,
[dir="rtl"] .card-body,
[dir="rtl"] .card-footer {
    text-align: right;
}

/* Only reverse flex direction for specific navigation elements */
[dir="rtl"] .navbar .d-flex {
    flex-direction: row-reverse;
}

[dir="rtl"] .d-flex.flex-column {
    flex-direction: column;
}

/* Don't reverse flex direction for general content */
[dir="rtl"] .card .d-flex {
    flex-direction: row;
}

/* User name column alignment */
.user-name-column {
    vertical-align: middle;
    padding: 0.75rem;
}

/* Ensure consistent table cell text alignment */
[dir="rtl"] .table td,
.rtl-layout .table td {
    text-align: right;
}

[dir="ltr"] .table td,
.table td {
    text-align: left;
}

/* Override for center-aligned columns */
[dir="rtl"] .table td.text-center,
[dir="ltr"] .table td.text-center,
.rtl-layout .table td.text-center,
.table td.text-center {
    text-align: center !important;
}

/* Ensure name text is properly aligned */
.user-name-column .text-start {
    text-align: left !important;
}

.user-name-column .text-end {
    text-align: right !important;
}

/* Profile avatar consistent sizing */
.user-name-column .profile-avatar {
    flex-shrink: 0;
}

/* Ensure table headers are properly aligned */
[dir="rtl"] .table th,
.rtl-layout .table th {
    text-align: right;
}

[dir="ltr"] .table th,
.table th {
    text-align: left;
}

/* Center-aligned headers remain centered */
[dir="rtl"] .table th.text-center,
[dir="ltr"] .table th.text-center,
.rtl-layout .table th.text-center,
.table th.text-center {
    text-align: center !important;
}

/* Consistent vertical alignment for all table cells */
.table td,
.table th {
    vertical-align: middle;
}

/* Force RTL layout for Arabic users - more specific rules */
.rtl-layout .table,
.rtl-layout .table th,
.rtl-layout .table td {
    direction: rtl !important;
    text-align: right !important;
}

.rtl-layout .table .text-center {
    text-align: center !important;
}

.rtl-layout .user-name-column .text-end {
    text-align: right !important;
    direction: rtl !important;
}

.rtl-layout .justify-content-end {
    justify-content: flex-end !important;
}

/* Remove this rule as it can cause issues with flex layouts */

/* Ensure LTR for English */
body:not(.rtl-layout) .table,
body:not(.rtl-layout) .table th,
body:not(.rtl-layout) .table td {
    direction: ltr;
    text-align: left;
}

body:not(.rtl-layout) .user-name-column .text-start {
    text-align: left !important;
    direction: ltr;
}

/* Comprehensive RTL fixes */
.rtl-layout {
    direction: rtl !important;
}

.rtl-layout * {
    direction: inherit;
}

.rtl-layout .table-responsive {
    direction: rtl !important;
}

.rtl-layout .card {
    direction: rtl !important;
}

.rtl-layout .card-header,
.rtl-layout .card-body {
    text-align: right !important;
}

/* Fix Bootstrap flex utilities in RTL */
.rtl-layout .justify-content-start {
    justify-content: flex-end !important;
}

.rtl-layout .justify-content-end {
    justify-content: flex-start !important;
}

.rtl-layout .text-start {
    text-align: right !important;
}

.rtl-layout .text-end {
    text-align: left !important;
}

/* Ensure icons maintain proper spacing in RTL */
[dir="rtl"] .fas,
[dir="rtl"] .far,
[dir="rtl"] .fab {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* RTL Table Support */
[dir="rtl"] .table {
    text-align: right;
}

[dir="rtl"] .table th,
[dir="rtl"] .table td {
    text-align: right;
}

[dir="rtl"] .table th.text-center,
[dir="rtl"] .table td.text-center {
    text-align: center !important;
}

[dir="rtl"] .table th.text-start,
[dir="rtl"] .table td.text-start {
    text-align: right !important;
}

[dir="rtl"] .table th.text-end,
[dir="rtl"] .table td.text-end {
    text-align: left !important;
}

/* RTL Button Groups */
[dir="rtl"] .btn-group {
    flex-direction: row-reverse;
}

/* RTL Form Controls */
[dir="rtl"] .form-control,
[dir="rtl"] .form-select {
    text-align: right;
}

/* RTL Dropdown */
[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
    text-align: right;
}

/* RTL Avatar and User Info */
[dir="rtl"] .avatar-sm {
    margin-left: 0.5rem;
    margin-right: 0;
}

/* RTL Badge and Status */
[dir="rtl"] .badge {
    margin-left: 0.25rem;
    margin-right: 0;
}

/* RTL Card Header Actions */
[dir="rtl"] .card-header .d-flex {
    flex-direction: row;
}

[dir="rtl"] .justify-content-between {
    flex-direction: row;
}

/* ===== PREVENT HORIZONTAL OVERFLOW ===== */
html, body {
    max-width: 100vw;
    overflow-x: hidden;
}

.page {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
}

* {
    max-width: 100%;
}

/* ===== FADE IN ANIMATION ===== */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}
