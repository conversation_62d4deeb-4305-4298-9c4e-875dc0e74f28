# Employee Rating System | نظام تقييم الموظفين

<div align="center">

![Employee Rating System](https://img.shields.io/badge/Employee%20Rating%20System-v2.0.0-blue)
![.NET](https://img.shields.io/badge/.NET-8.0-purple)
![Blazor](https://img.shields.io/badge/Blazor-Server-orange)
![License](https://img.shields.io/badge/License-MIT-green)

**A comprehensive bilingual employee performance evaluation system**
**نظام شامل ثنائي اللغة لتقييم أداء الموظفين**

[English](#english) | [العربية](#arabic)

</div>

---

## English

### 🎯 Project Overview

The Employee Rating System is a modern, comprehensive web application designed for organizations to manage employee performance evaluations efficiently. Built with C# Blazor Server technology, it provides a robust, scalable solution for performance management with full bilingual support (Arabic/English) and role-based access control.

**Key Highlights:**
- ✅ **Complete System**: 100% feature parity with enterprise requirements
- ✅ **Bilingual Interface**: Full Arabic/English support with RTL layout
- ✅ **Role-Based Security**: 4-tier access control system
- ✅ **Modern Architecture**: Built on .NET 8.0 Blazor Server
- ✅ **Production Ready**: Enterprise-grade security and scalability

## 🌟 Key Features

### 🏢 Hierarchical Department Management
- **Unlimited Nesting**: Support for unlimited department hierarchy levels
- **Interactive Tree View**: Expandable/collapsible department visualization
- **Many-to-Many Relationships**: Employees can belong to multiple departments
- **Dynamic Restructuring**: Real-time organizational changes without data loss
- **Manager Assignment**: Department-level manager designation and access control

### 👥 Advanced Role-Based Access Control
- **SUPER_ADMIN**: Complete system administration and configuration
- **MANAGER**: Department hierarchy management and oversight
- **SUPERVISOR**: Direct team management and employee evaluation
- **QUALITY_TEAM**: Cross-departmental quality monitoring (read-only)
- **EMPLOYEE**: Personal performance data and feedback access

### 📊 Configurable Evaluation System
- **Dynamic Categories**: Configurable evaluation categories with adjustable weights (must total 100%)
- **Bilingual Questions**: Questions and criteria in both Arabic and English
- **Weighted Scoring**: Automatic calculation based on category and question weights
- **Flexible Workflow**: Draft → Submit → Approve/Reject evaluation process
- **Real-Time Calculation**: Instant score updates during evaluation entry

### 🚀 Bulk Evaluation Interface
- **4-Step Workflow**: Period Selection → Employee Selection → Score Input → Review & Submit
- **Table-Based Entry**: Efficient bulk scoring interface for multiple employees
- **Progress Tracking**: Visual step indicators and completion status
- **Validation**: Real-time validation and error handling
- **Batch Processing**: Submit multiple evaluations simultaneously

### 🌐 Complete Bilingual Support (Arabic/English)
- **RTL Layout**: Proper right-to-left layout for Arabic content
- **Language Toggle**: Instant switching with E/ع button in navigation
- **Persistent Preferences**: Language choice saved via cookies
- **Cultural Adaptation**: Date formats, number formatting, and text direction
- **Bilingual Data**: All content available in both languages

### 📈 Dashboard & Analytics
- **Role-Based Dashboards**: Customized views based on user role and access level
- **Real-Time Statistics**: Live data updates with comprehensive metrics
- **Performance Analytics**: Trend analysis and performance indicators
- **Visual Reports**: Charts, progress bars, and interactive data visualization
- **Department Insights**: Hierarchical performance analysis

## 🚀 Quick Start

### Prerequisites
- **.NET 8.0 SDK** or later
- **Visual Studio 2022** (recommended) or **Visual Studio Code**
- **SQLite** (development) or **PostgreSQL/SQL Server** (production)

### Installation

1. **Clone and Navigate**
   ```bash
   git clone <repository-url>
   cd blazor_app
   ```

2. **Restore Dependencies**
   ```bash
   dotnet restore
   ```

3. **Setup Database**
   ```bash
   dotnet ef database update
   ```

4. **Run Application**
   ```bash
   dotnet run
   ```

5. **Access Application**
   - Open browser to: `http://localhost:5222`
   - Login with demo credentials (see [DEMO_CREDENTIALS.md](DEMO_CREDENTIALS.md))

## 🔐 Demo Credentials

All demo accounts use ****word: **`Password123!`**

| Role | Email | Access Level |
|------|-------|--------------|
| **Super Admin** | `<EMAIL>` | Full system access |
| **Manager** | `<EMAIL>` | Department hierarchy management |
| **Supervisor** | `<EMAIL>` | Direct reports only |
| **Quality Team** | `<EMAIL>` | Read-only cross-department |
| **Employee** | `<EMAIL>` | Personal data only |

## 🏗️ Technology Stack

- **Framework**: ASP.NET Core 8.0 Blazor Server
- **Database**: Entity Framework Core 8.0 with multi-provider support
- **Authentication**: ASP.NET Core Identity with custom user model
- **UI Framework**: Bootstrap 5.3.0 with Font Awesome 6.0.0
- **Localization**: Built-in ASP.NET Core localization with RTL support
- **Real-Time**: Blazor Server with SignalR for live updates

### Database Support
- **Development**: SQLite (lightweight, file-based)
- **Production**: PostgreSQL or SQL Server
- **Configuration**: Environment variable based switching

## 📁 Project Architecture

```
blazor_app/
├── Components/
│   ├── Layout/           # Navigation, main layout, RTL support
│   ├── Pages/            # All application pages and routes
│   │   ├── Account/      # Authentication (Login, Register, Logout)
│   │   ├── Dashboard/    # Role-based dashboard views
│   │   ├── Departments/  # Department management and tree view
│   │   ├── Users/        # User management and profiles
│   │   └── Evaluations/  # Individual and bulk evaluations
│   ├── Shared/           # Reusable UI components
│   ├── Departments/      # Department-specific components
│   ├── Users/            # User management components
│   └── Evaluations/      # Evaluation system components
├── Data/                 # Entity Framework context and configuration
├── Models/               # All entity models with relationships
├── Services/             # Business logic and data services
├── Resources/            # Localization resources
├── wwwroot/              # Static files (CSS, JS, images)
├── Migrations/           # Database migrations
└── Documentation/        # Setup guides and testing checklists
```

## 🔧 Configuration & Setup

### Database Configuration
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Data Source=employee_rating.db",
    "PostgreSQL": "Host=localhost;Database=employee_rating;Username=user;Password=****",
    "SqlServer": "Server=localhost;Database=EmployeeRating;Trusted_Connection=true"
  },
  "DATABASE_ENGINE": "sqlite"
}
```

### Environment Variables
```env
DATABASE_ENGINE=sqlite|postgresql|sqlserver
POSTGRES_HOST=localhost
POSTGRES_DB=employee_rating
POSTGRES_USER=your_user
POSTGRES_PASSWORD=your_****word
ASPNETCORE_ENVIRONMENT=Development|Production
```

## 📊 Core Functionality

### 1. Department Hierarchy Management
- **Unlimited Nesting**: Create complex organizational structures
- **Visual Tree Component**: Interactive department tree with expand/collapse
- **Manager Assignment**: Assign managers to any department level
- **Employee Distribution**: Many-to-many employee-department relationships
- **Access Control**: Hierarchical access inheritance

### 2. Advanced User Management
- **Bilingual Profiles**: English and Arabic names for all users
- **Role Assignment**: 5 distinct roles with specific permissions
- **Department Mapping**: Primary and secondary department assignments
- **Search & Filter**: Advanced filtering by role, department, status
- **Bulk Operations**: Efficient management of multiple users

### 3. Sophisticated Evaluation System
- **Configurable Categories**: Dynamic evaluation categories with weights
- **Question Management**: Bilingual questions with scoring scales
- **Individual Evaluations**: Complete evaluation workflow with approval
- **Bulk Evaluations**: 4-step process for evaluating multiple employees
- **Score Calculation**: Real-time weighted scoring with validation

### 4. Comprehensive Reporting
- **Dashboard Analytics**: Role-based dashboard with real-time metrics
- **Performance Trends**: Historical analysis and trend visualization
- **Department Reports**: Hierarchical performance analysis
- **Export Functionality**: PDF and Excel export capabilities
- **Quality Metrics**: Cross-departmental quality monitoring

## 🔒 Security & Access Control

### Authentication Security
- **ASP.NET Core Identity**: Industry-standard authentication
- **Password Security**: Hashing, salting, and complexity requirements
- **Session Management**: Secure session handling and timeout
- **CSRF Protection**: Built-in cross-site request forgery protection

### Authorization Controls
- **Role-Based Access**: Strict role-based permission system
- **Hierarchical Inheritance**: Access to subordinate organizational levels
- **Department Boundaries**: No cross-department access outside hierarchy
- **Data Isolation**: User data visibility based on role and department
- **Audit Trail**: Complete tracking of all system changes

## 📱 Responsive Design & Accessibility

### Desktop Experience
- **Professional Interface**: Clean, modern Bootstrap 5 design
- **Full Functionality**: Complete feature set optimized for large screens
- **Keyboard Navigation**: Full keyboard accessibility support
- **Performance Optimized**: Fast loading and responsive interactions

### Mobile Experience
- **Responsive Layout**: Adaptive design for all screen sizes
- **Touch-Friendly**: Optimized for mobile interaction
- **Mobile Navigation**: Collapsible menu system
- **Form Optimization**: Mobile-optimized form layouts

### Accessibility Features
- **WCAG 2.1 Compliance**: Accessibility standards compliance
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **High Contrast**: Support for high contrast modes
- **Bilingual Accessibility**: Accessible in both Arabic and English

## 🌐 Deployment Options

### Development
```bash
dotnet run --environment Development
```

### Production
```bash
dotnet publish -c Release -o ./publish
# Deploy to IIS, Linux, or cloud platform
```

### Docker Deployment
```bash
docker build -t employee-rating .
docker run -p 8080:80 employee-rating
```

### Cloud Deployment
- **Azure App Service**: Direct deployment support
- **AWS Elastic Beanstalk**: .NET Core deployment
- **Google Cloud Run**: Containerized deployment

## 📋 Comprehensive Documentation

| Document | Description |
|----------|-------------|
| **[SETUP_BLAZOR.md](SETUP_BLAZOR.md)** | Detailed installation and configuration guide |
| **[DEMO_CREDENTIALS.md](DEMO_CREDENTIALS.md)** | Test accounts and access levels |
| **[TESTING_CHECKLIST.md](TESTING_CHECKLIST.md)** | Comprehensive testing guide (150+ test cases) |
| **[BLAZOR_IMPLEMENTATION_SUMMARY.md](BLAZOR_IMPLEMENTATION_SUMMARY.md)** | Technical architecture overview |
| **[CONVERSION_COMPLETE.md](CONVERSION_COMPLETE.md)** | Django to Blazor conversion summary |

## 🧪 Testing & Quality Assurance

### Automated Testing
```bash
dotnet test
```

### Manual Testing Checklist
- ✅ **Authentication & Authorization** (5 roles, hierarchical access)
- ✅ **Department Management** (unlimited nesting, tree visualization)
- ✅ **User Management** (CRUD operations, role assignment)
- ✅ **Evaluation System** (individual and bulk workflows)
- ✅ **Bilingual Support** (Arabic RTL, language switching)
- ✅ **Dashboard & Reporting** (analytics, performance metrics)
- ✅ **Security** (access control, data protection)
- ✅ **Responsive Design** (desktop and mobile)

### Demo Testing
Use the provided demo credentials to test all functionality across different user roles and access levels.

## 🔄 Migration from Django

### Complete Feature Parity
- ✅ **All Django Models** → C# Entity Framework models
- ✅ **Django Views** → Blazor pages and components
- ✅ **Django Templates** → Blazor components with RTL support
- ✅ **Django Forms** → Blazor form validation
- ✅ **Django Admin** → Custom management interfaces
- ✅ **Django Permissions** → ASP.NET Core authorization

### Enhanced Features
- **Real-Time Updates**: Blazor Server with SignalR
- **Better Performance**: Optimized queries and caching
- **Improved Security**: Built-in ASP.NET Core protections
- **Modern UI**: Bootstrap 5 with enhanced user experience
- **Scalable Architecture**: Enterprise-ready design patterns

## 📈 Performance & Scalability

### Optimizations
- **Efficient Queries**: Optimized Entity Framework queries
- **Lazy Loading**: Strategic lazy loading for performance
- **Caching**: Memory caching for frequently accessed data
- **Connection Pooling**: Database connection optimization
- **Minimal JavaScript**: Blazor Server reduces client-side complexity

### Monitoring
- **Built-in Logging**: Comprehensive application logging
- **Health Checks**: Application health monitoring
- **Performance Metrics**: Response time and throughput tracking
- **Error Handling**: Graceful error handling and recovery

## 🤝 Contributing

1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

## 📞 Support & Resources

### Documentation
- [ASP.NET Core Blazor](https://docs.microsoft.com/aspnet/core/blazor)
- [Entity Framework Core](https://docs.microsoft.com/ef/core)
- [Bootstrap 5](https://getbootstrap.com/docs/5.3)

### Community
- [ASP.NET Core GitHub](https://github.com/dotnet/aspnetcore)
- [Blazor Community](https://blazor.net)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/blazor)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🎉 Acknowledgments

- **ASP.NET Core Team** for the excellent Blazor framework
- **Bootstrap Team** for the responsive UI framework
- **Font Awesome** for the comprehensive icon library
- **Entity Framework Team** for the powerful ORM
- **Original Django Project** for the business requirements and inspiration

---

## 🚀 Ready for Production!

This Employee Rating System provides a **complete, scalable, and production-ready** solution for employee performance management with:

- ✅ **100% Feature Parity** with the original Django system
- ✅ **Enhanced Performance** and user experience
- ✅ **Enterprise-Grade Security** and access control
- ✅ **Full Bilingual Support** with proper RTL implementation
- ✅ **Comprehensive Documentation** and testing guides
- ✅ **Modern Architecture** built for scalability and maintainability

**🎯 The conversion is complete and the application is ready for immediate deployment and use!**